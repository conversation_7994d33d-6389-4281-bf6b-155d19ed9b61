#!/usr/bin/env python3
"""
Simple test script to verify imports and basic functionality
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test basic imports"""
    try:
        print("Testing basic imports...")
        
        # Test config import
        from jimeng_mcp_server.config import config
        print(f"✓ Config imported successfully. Default model: {config.default_model}")
        
        # Test core imports
        from jimeng_mcp_server.core import TokenManager, JimengClient
        print("✓ Core modules imported successfully")
        
        # Test tools imports
        from jimeng_mcp_server.tools import ChatTool, ImageTool, CreditTool
        print("✓ Tools imported successfully")
        
        # Test server import
        from jimeng_mcp_server.server import JimengMCPServer
        print("✓ Server imported successfully")
        
        print("\n✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_token_manager():
    """Test token manager functionality"""
    try:
        print("\nTesting TokenManager...")
        
        from jimeng_mcp_server.core.auth import TokenManager, parse_tokens_from_auth_header
        
        # Test token parsing
        tokens = parse_tokens_from_auth_header("Bearer token1,token2,token3")
        assert tokens == ["token1", "token2", "token3"], f"Expected ['token1', 'token2', 'token3'], got {tokens}"
        print("✓ Token parsing works correctly")
        
        # Test token manager
        tm = TokenManager(["test_token1", "test_token2"])
        assert tm.has_tokens(), "Token manager should have tokens"
        assert len(tm.tokens) == 2, f"Expected 2 tokens, got {len(tm.tokens)}"
        
        random_token = tm.get_random_token()
        assert random_token in ["test_token1", "test_token2"], f"Random token should be one of the configured tokens"
        print("✓ TokenManager works correctly")
        
        # Test cookie generation
        cookie = tm.generate_cookie("test_session_id")
        assert "sessionid=test_session_id" in cookie, "Cookie should contain session ID"
        print("✓ Cookie generation works correctly")
        
        print("✅ TokenManager tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ TokenManager test failed: {e}")
        return False

def test_tool_definitions():
    """Test tool definitions"""
    try:
        print("\nTesting tool definitions...")
        
        from jimeng_mcp_server.tools import ChatTool, ImageTool, CreditTool
        
        # Test image tool definition
        image_def = ImageTool.get_tool_definition()
        assert image_def["name"] == "jimeng_generate_images", "Image tool name should be correct"
        assert "inputSchema" in image_def, "Image tool should have input schema"
        print("✓ Image tool definition is valid")
        
        # Test chat tool definition
        chat_def = ChatTool.get_tool_definition()
        assert chat_def["name"] == "jimeng_chat_completion", "Chat tool name should be correct"
        assert "inputSchema" in chat_def, "Chat tool should have input schema"
        print("✓ Chat tool definition is valid")
        
        # Test credit tool definitions
        credit_def = CreditTool.get_credit_info_tool_definition()
        assert credit_def["name"] == "jimeng_get_credit", "Credit tool name should be correct"
        print("✓ Credit tool definition is valid")
        
        print("✅ Tool definition tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Tool definition test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Running Jimeng MCP Server Tests\n")
    
    tests = [
        test_imports,
        test_token_manager,
        test_tool_definitions,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The MCP Server is ready to use.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
