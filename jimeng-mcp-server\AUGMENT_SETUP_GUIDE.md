# Augment Code MCP 客户端配置指南

## 前提条件

1. ✅ 已安装 Augment Code VS Code 扩展
2. ✅ Jimeng MCP Server 已正确安装和测试
3. ✅ 有效的即梦 session token

## 配置步骤

### 方法一：通过 settings.json 配置（推荐）

#### 1. 打开 Augment 设置
- 按 `Ctrl+Shift+P` (Windows) 或 `Cmd+Shift+P` (Mac)
- 输入 "Augment: Edit Settings"
- 选择 "Edit Settings"
- 点击 "Advanced" 部分的 "Edit in settings.json"

#### 2. 添加 MCP 服务器配置

在 `settings.json` 中添加或修改 `augment.advanced` 部分：

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "jimeng",
        "command": "uv",
        "args": [
          "run",
          "jimeng-mcp-server",
          "--tokens",
          "您的session_token",
          "--log-level",
          "INFO"
        ],
        "cwd": "您的项目路径"
      }
    ]
  }
}
```

#### 3. 替换配置值

**必须替换的值：**
- `您的session_token`: 替换为您的真实 session token
- `您的项目路径`: 替换为 jimeng-mcp-server 的完整路径

**示例配置：**
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "jimeng",
        "command": "uv",
        "args": [
          "run",
          "jimeng-mcp-server",
          "--tokens",
          "9d9bb27eda4f31ceb509b8dcda98ebca",
          "--log-level",
          "INFO"
        ],
        "cwd": "E:\\Projects\\jimeng-free-api\\jimeng-mcp-server"
      }
    ]
  }
}
```

### 方法二：通过设置面板配置

#### 1. 打开设置面板
- 点击 Augment 面板右上角的齿轮图标 ⚙️
- 找到 "MCP" 部分

#### 2. 添加新服务器
- 点击 MCP 标题旁的 `+` 按钮
- 填写以下信息：
  - **Name**: `jimeng`
  - **Command**: `uv run jimeng-mcp-server --tokens 您的token --log-level INFO`
  - **Working Directory**: `您的项目路径`

## 重启和验证

### 1. 重启 VS Code
配置完成后，重启 VS Code 以加载 MCP 服务器。

### 2. 验证连接
1. 打开 Augment Agent 面板
2. 开始新的对话
3. 尝试使用即梦相关的命令，例如：
   - "帮我生成一张可爱的熊猫图片"
   - "检查我的即梦积分余额"

### 3. 检查可用工具
在 Augment Agent 中，您应该能够使用以下工具：
- `jimeng_generate_images` - 图像生成
- `jimeng_chat_completion` - 聊天补全
- `jimeng_get_credit` - 获取积分信息
- `jimeng_receive_credit` - 领取每日积分
- `jimeng_check_token` - 检查 token 状态

## 故障排除

### 常见问题

#### 1. MCP 服务器未启动
**症状**: Augment 无法连接到 jimeng 服务器
**解决方案**:
- 检查 `cwd` 路径是否正确
- 确保 `uv` 命令在系统 PATH 中
- 验证 token 是否有效

#### 2. 权限错误
**症状**: 无法执行 uv 命令
**解决方案**:
- 确保 uv 已正确安装
- 检查项目目录的读写权限

#### 3. Token 无效
**症状**: 工具调用失败，提示认证错误
**解决方案**:
- 重新获取 session token
- 检查 token 格式是否正确

### 调试步骤

#### 1. 检查 MCP 服务器状态
```bash
cd E:\Projects\jimeng-free-api\jimeng-mcp-server
uv run jimeng-mcp-server --tokens 您的token --log-level DEBUG
```

#### 2. 查看 Augment 日志
- 在 VS Code 中打开 "Output" 面板
- 选择 "Augment" 频道
- 查看 MCP 连接日志

#### 3. 测试工具功能
在 Augment Agent 中尝试：
```
请使用 jimeng_get_credit 工具检查我的积分余额
```

## 使用示例

### 图像生成
```
请帮我生成一张1024x1024的图片，内容是：一只可爱的熊猫在竹林中玩耍，阳光透过竹叶洒下来，画面温馨美好。
```

### 积分管理
```
请检查我的即梦积分余额，如果积分不足请帮我领取每日积分。
```

### 模型选择
```
使用 jimeng-2.1 模型生成一张科幻风格的城市景观图片。
```

## 高级配置

### 多 Token 配置
如果您有多个 session token，可以这样配置：

```json
{
  "name": "jimeng",
  "command": "uv",
  "args": [
    "run",
    "jimeng-mcp-server",
    "--tokens",
    "token1,token2,token3",
    "--log-level",
    "INFO"
  ],
  "cwd": "您的项目路径"
}
```

### 使用配置文件
也可以使用 token 文件：

```json
{
  "name": "jimeng",
  "command": "uv",
  "args": [
    "run",
    "jimeng-mcp-server",
    "--token-file",
    "examples/tokens.txt",
    "--log-level",
    "INFO"
  ],
  "cwd": "您的项目路径"
}
```

## 支持和反馈

如果遇到问题，请：
1. 检查本指南的故障排除部分
2. 查看项目的 GitHub Issues
3. 在 Augment Code Discord 社区寻求帮助
