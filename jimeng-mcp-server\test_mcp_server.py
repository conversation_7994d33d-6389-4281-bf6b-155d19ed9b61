#!/usr/bin/env python3
"""
Test script to verify MCP server functionality
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_server_initialization():
    """Test server initialization without running the full server"""
    try:
        print("Testing MCP server initialization...")
        
        from jimeng_mcp_server.server import JimengMCPServer
        
        # Test server creation without tokens
        server = JimengMCPServer()
        print("✓ Server created successfully without tokens")
        
        # Test server creation with mock tokens
        mock_tokens = ["test_token_1", "test_token_2"]
        server_with_tokens = JimengMCPServer(mock_tokens)
        print("✓ Server created successfully with tokens")
        
        # Test client initialization
        await server_with_tokens._initialize_client()
        print("✓ Client initialized successfully")
        
        # Test that tools are properly initialized
        assert server_with_tokens.chat_tool is not None, "Chat tool should be initialized"
        assert server_with_tokens.image_tool is not None, "Image tool should be initialized"
        assert server_with_tokens.credit_tool is not None, "Credit tool should be initialized"
        print("✓ All tools initialized successfully")
        
        # Test token manager
        assert len(server_with_tokens.token_manager.tokens) == 2, "Should have 2 tokens"
        print("✓ Token manager configured correctly")
        
        # Cleanup
        await server_with_tokens.close()
        print("✓ Server cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Server initialization test failed: {e}")
        return False

async def test_tool_definitions():
    """Test tool definitions and schemas"""
    try:
        print("\nTesting tool definitions...")

        from jimeng_mcp_server.tools import ChatTool, ImageTool, CreditTool

        # Test individual tool definitions
        chat_def = ChatTool.get_tool_definition()
        assert chat_def["name"] == "jimeng_chat_completion", "Chat tool name should be correct"
        assert "inputSchema" in chat_def, "Chat tool should have input schema"
        print("✓ Chat tool definition is valid")

        image_def = ImageTool.get_tool_definition()
        assert image_def["name"] == "jimeng_generate_images", "Image tool name should be correct"
        assert "inputSchema" in image_def, "Image tool should have input schema"
        print("✓ Image tool definition is valid")

        credit_def = CreditTool.get_credit_info_tool_definition()
        assert credit_def["name"] == "jimeng_get_credit", "Credit tool name should be correct"
        assert "inputSchema" in credit_def, "Credit tool should have input schema"
        print("✓ Credit info tool definition is valid")

        receive_def = CreditTool.get_receive_credit_tool_definition()
        assert receive_def["name"] == "jimeng_receive_credit", "Receive credit tool name should be correct"
        assert "inputSchema" in receive_def, "Receive credit tool should have input schema"
        print("✓ Receive credit tool definition is valid")

        token_def = CreditTool.get_token_status_tool_definition()
        assert token_def["name"] == "jimeng_check_token", "Token check tool name should be correct"
        assert "inputSchema" in token_def, "Token check tool should have input schema"
        print("✓ Token check tool definition is valid")

        print("✓ All 5 tools have valid definitions")

        return True

    except Exception as e:
        print(f"❌ Tool definitions test failed: {e}")
        return False

async def test_token_handling():
    """Test token handling functionality"""
    try:
        print("\nTesting token handling...")
        
        from jimeng_mcp_server.core.auth import TokenManager, parse_tokens_from_auth_header
        
        # Test token parsing from different formats
        tokens1 = parse_tokens_from_auth_header("Bearer token1,token2,token3")
        assert tokens1 == ["token1", "token2", "token3"], "Token parsing failed"
        print("✓ Token parsing from Bearer header works")
        
        # Test token manager with multiple tokens
        tm = TokenManager(["token1", "token2", "token3"])
        assert tm.has_tokens(), "Token manager should have tokens"
        assert len(tm.tokens) == 3, "Should have 3 tokens"
        print("✓ Token manager initialization works")
        
        # Test random token selection
        random_token = tm.get_random_token()
        assert random_token in tm.tokens, "Random token should be from configured tokens"
        print("✓ Random token selection works")
        
        # Test adding new token
        tm.add_token("new_token")
        assert "new_token" in tm.tokens, "New token should be added"
        assert len(tm.tokens) == 4, "Should have 4 tokens after adding"
        print("✓ Token addition works")
        
        return True
        
    except Exception as e:
        print(f"❌ Token handling test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Running Jimeng MCP Server Integration Tests\n")
    
    tests = [
        test_server_initialization,
        test_tool_definitions,
        test_token_handling,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if await test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All MCP server tests passed! The server is ready for use.")
        print("\n📝 Next steps:")
        print("1. Get session tokens from https://jimeng.jianying.com/")
        print("2. Configure tokens in examples/tokens.txt or use --tokens parameter")
        print("3. Test with a real MCP client like Claude Desktop")
        print("4. Example usage:")
        print("   uv run jimeng-mcp-server --token-file examples/tokens.txt")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
