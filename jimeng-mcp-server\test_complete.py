#!/usr/bin/env python3
"""
Complete test suite for Jimeng MCP Server
"""

import asyncio
import sys
import os
import subprocess

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_dependencies():
    """Test that all required dependencies are installed"""
    try:
        print("Testing dependencies...")
        
        import mcp
        print("✓ MCP library available")
        
        import httpx
        print("✓ HTTPX library available")
        
        import pydantic
        print("✓ Pydantic library available")
        
        import anyio
        print("✓ AnyIO library available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False

def test_cli_interface():
    """Test command line interface"""
    try:
        print("\nTesting CLI interface...")
        
        # Test help command
        result = subprocess.run(
            ["uv", "run", "jimeng-mcp-server", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✓ CLI help command works")
            assert "Jimeng MCP Server" in result.stdout, "Help should contain server name"
            assert "--tokens" in result.stdout, "Help should mention tokens option"
            assert "--token-file" in result.stdout, "Help should mention token-file option"
            print("✓ CLI help content is correct")
        else:
            print(f"❌ CLI help failed: {result.stderr}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        return False

async def test_core_functionality():
    """Test core server functionality"""
    try:
        print("\nTesting core functionality...")
        
        from jimeng_mcp_server.config import config
        from jimeng_mcp_server.core import TokenManager, JimengClient
        from jimeng_mcp_server.tools import ChatTool, ImageTool, CreditTool
        from jimeng_mcp_server.server import JimengMCPServer
        
        # Test configuration
        assert config.default_model in config.available_models, "Default model should be in available models"
        print("✓ Configuration is valid")
        
        # Test token manager
        tm = TokenManager(["test_token"])
        assert tm.has_tokens(), "Token manager should have tokens"
        print("✓ Token manager works")
        
        # Test client creation
        client = JimengClient(tm)
        assert client.token_manager == tm, "Client should use provided token manager"
        print("✓ Client creation works")
        
        # Test tools creation
        chat_tool = ChatTool(client)
        image_tool = ImageTool(client)
        credit_tool = CreditTool(client)
        print("✓ Tools creation works")
        
        # Test server creation and initialization
        server = JimengMCPServer(["test_token"])
        await server._initialize_client()
        
        assert server.client is not None, "Server should have client"
        assert server.chat_tool is not None, "Server should have chat tool"
        assert server.image_tool is not None, "Server should have image tool"
        assert server.credit_tool is not None, "Server should have credit tool"
        print("✓ Server initialization works")
        
        # Cleanup
        await server.close()
        print("✓ Server cleanup works")
        
        return True
        
    except Exception as e:
        print(f"❌ Core functionality test failed: {e}")
        return False

def test_project_structure():
    """Test project structure and files"""
    try:
        print("\nTesting project structure...")
        
        # Check required files exist
        required_files = [
            "pyproject.toml",
            "README.md",
            "src/jimeng_mcp_server/__init__.py",
            "src/jimeng_mcp_server/server.py",
            "src/jimeng_mcp_server/config.py",
            "src/jimeng_mcp_server/core/__init__.py",
            "src/jimeng_mcp_server/tools/__init__.py",
            "examples/tokens.txt",
            "examples/client_config.json"
        ]
        
        for file_path in required_files:
            full_path = os.path.join(os.path.dirname(__file__), file_path)
            assert os.path.exists(full_path), f"Required file missing: {file_path}"
        
        print("✓ All required files exist")
        
        # Check that examples directory has proper structure
        examples_dir = os.path.join(os.path.dirname(__file__), "examples")
        assert os.path.isdir(examples_dir), "Examples directory should exist"
        print("✓ Examples directory exists")
        
        return True
        
    except Exception as e:
        print(f"❌ Project structure test failed: {e}")
        return False

def test_tool_schemas():
    """Test that all tools have valid schemas"""
    try:
        print("\nTesting tool schemas...")
        
        from jimeng_mcp_server.tools import ChatTool, ImageTool, CreditTool
        
        # Test all tool definitions
        tools_to_test = [
            (ChatTool.get_tool_definition, "jimeng_chat_completion"),
            (ImageTool.get_tool_definition, "jimeng_generate_images"),
            (CreditTool.get_credit_info_tool_definition, "jimeng_get_credit"),
            (CreditTool.get_receive_credit_tool_definition, "jimeng_receive_credit"),
            (CreditTool.get_token_status_tool_definition, "jimeng_check_token"),
        ]
        
        for tool_func, expected_name in tools_to_test:
            definition = tool_func()
            
            assert "name" in definition, f"Tool definition should have name"
            assert definition["name"] == expected_name, f"Tool name should be {expected_name}"
            assert "description" in definition, f"Tool should have description"
            assert "inputSchema" in definition, f"Tool should have input schema"
            
            schema = definition["inputSchema"]
            assert "type" in schema, f"Schema should have type"
            assert schema["type"] == "object", f"Schema type should be object"
            
            print(f"✓ Tool {expected_name} has valid schema")
        
        print("✓ All tool schemas are valid")
        return True
        
    except Exception as e:
        print(f"❌ Tool schemas test failed: {e}")
        return False

async def main():
    """Run complete test suite"""
    print("🧪 Running Complete Jimeng MCP Server Test Suite\n")
    
    tests = [
        ("Dependencies", test_dependencies),
        ("CLI Interface", test_cli_interface),
        ("Core Functionality", test_core_functionality),
        ("Project Structure", test_project_structure),
        ("Tool Schemas", test_tool_schemas),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} test passed")
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
        
        print()
    
    print(f"📊 Final Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Jimeng MCP Server is fully functional!")
        print("\n🚀 Ready for production use:")
        print("1. ✅ All dependencies are properly installed")
        print("2. ✅ CLI interface is working correctly")
        print("3. ✅ Core functionality is operational")
        print("4. ✅ Project structure is complete")
        print("5. ✅ All tool schemas are valid")
        print("\n📋 Usage Instructions:")
        print("• Get session tokens from https://jimeng.jianying.com/")
        print("• Add tokens to examples/tokens.txt or use --tokens parameter")
        print("• Run: uv run jimeng-mcp-server --token-file examples/tokens.txt")
        print("• Configure in Claude Desktop or other MCP clients")
        return True
    else:
        print(f"❌ {total - passed} tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
