#!/usr/bin/env python3
"""
Startup script for Jimeng MCP Server
This script ensures the server runs with the correct Python environment
"""

import sys
import os

# Add src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

# Import and run the server
if __name__ == "__main__":
    from jimeng_mcp_server.server import cli_main
    cli_main()
