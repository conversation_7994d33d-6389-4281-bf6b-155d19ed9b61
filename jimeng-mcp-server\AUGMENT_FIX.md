# 🔧 Augment Code MCP 配置修复指南

## 问题描述
错误：`ModuleNotFoundError: No module named 'jimeng_mcp_server'`

**原因**：Augment Code 使用系统 Python 而不是项目虚拟环境。

## ✅ 解决方案

### 方法 1：使用启动脚本（推荐）

我已经创建了 `start_mcp_server.py` 启动脚本来解决这个问题。

**在 Augment Code 中配置：**

#### 设置面板方式：
- **Name**: `jimeng`
- **Command**: `uv run python start_mcp_server.py --tokens 9d9bb27eda4f31ceb509b8dcda98ebca --log-level INFO`
- **Environment Variables**:
  - `cwd`: `E:\Projects\jimeng-free-api\jimeng-mcp-server`

#### settings.json 方式：
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "jimeng",
        "command": "uv",
        "args": [
          "run",
          "python",
          "start_mcp_server.py",
          "--tokens",
          "9d9bb27eda4f31ceb509b8dcda98ebca",
          "--log-level",
          "INFO"
        ],
        "cwd": "E:\\Projects\\jimeng-free-api\\jimeng-mcp-server"
      }
    ]
  }
}
```

### 方法 2：使用批处理文件（Windows）

我也创建了 `start_mcp_server.bat` 文件。

**配置：**
- **Command**: `start_mcp_server.bat --tokens 9d9bb27eda4f31ceb509b8dcda98ebca --log-level INFO`

### 方法 3：直接使用 uv（备选）

如果上述方法不工作，可以尝试：
- **Command**: `uv --directory E:\Projects\jimeng-free-api\jimeng-mcp-server run jimeng-mcp-server --tokens 9d9bb27eda4f31ceb509b8dcda98ebca --log-level INFO`

## 🧪 验证配置

配置完成后：

1. **保存配置**
2. **重启 VS Code**
3. **在 Augment Agent 中测试**：
   ```
   帮我生成一张可爱的熊猫图片
   ```

## 📁 创建的文件

- `start_mcp_server.py` - Python 启动脚本
- `start_mcp_server.bat` - Windows 批处理文件
- `augment_mcp_config.json` - 完整配置文件

## 🔍 故障排除

### 如果仍然出错：

1. **检查路径**：确保 `cwd` 路径正确
2. **检查 uv**：确保 uv 在系统 PATH 中
3. **检查权限**：确保有执行权限
4. **查看日志**：在 VS Code Output 面板查看 Augment 日志

### 测试命令

在项目目录中运行以下命令验证：
```bash
uv run python start_mcp_server.py --help
```

应该显示帮助信息而不是错误。

## 🎯 关键变化

**之前（错误）**：
```
uv run python -m jimeng_mcp_server.server
```

**现在（正确）**：
```
uv run python start_mcp_server.py
```

启动脚本会自动处理模块路径问题。
