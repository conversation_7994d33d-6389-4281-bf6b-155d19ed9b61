#!/usr/bin/env python3
"""
Generate Augment Code MCP configuration for Jimeng MCP Server
"""

import json
import os
import sys

def generate_augment_config(token, project_path=None):
    """Generate Augment Code MCP configuration"""
    
    if project_path is None:
        project_path = os.path.abspath(os.path.dirname(__file__))
    
    # Normalize path for Windows
    if os.name == 'nt':
        project_path = project_path.replace('/', '\\')
    
    config = {
        "augment.advanced": {
            "mcpServers": [
                {
                    "name": "jimeng",
                    "command": "uv",
                    "args": [
                        "run",
                        "python",
                        "start_mcp_server.py",
                        "--tokens",
                        token,
                        "--log-level",
                        "INFO"
                    ],
                    "cwd": project_path
                }
            ]
        }
    }
    
    return config

def main():
    """Main function"""
    print("🔧 Jimeng MCP Server - Augment Code 配置生成器")
    print("=" * 50)
    
    # Get token from user or use default
    if len(sys.argv) > 1:
        token = sys.argv[1]
    else:
        token = "9d9bb27eda4f31ceb509b8dcda98ebca"  # Default token from previous test
    
    # Get project path
    project_path = os.path.abspath(os.path.dirname(__file__))
    
    print(f"📍 项目路径: {project_path}")
    print(f"🔑 使用 Token: {token[:8]}...")
    
    # Generate configuration
    config = generate_augment_config(token, project_path)
    
    # Save configuration to file
    config_file = os.path.join(project_path, "augment_mcp_config.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 配置文件已生成: {config_file}")
    
    # Display configuration
    print("\n📋 Augment Code settings.json 配置:")
    print("-" * 40)
    print(json.dumps(config, indent=2, ensure_ascii=False))
    
    print("\n📝 配置步骤:")
    print("1. 在 VS Code 中按 Ctrl+Shift+P (Windows) 或 Cmd+Shift+P (Mac)")
    print("2. 输入 'Augment: Edit Settings'")
    print("3. 选择 'Edit Settings'")
    print("4. 点击 'Advanced' 部分的 'Edit in settings.json'")
    print("5. 将上面的配置添加到您的 settings.json 文件中")
    print("6. 保存文件并重启 VS Code")
    
    print("\n🧪 测试命令:")
    print("在 Augment Agent 中尝试:")
    print("- '帮我生成一张可爱的熊猫图片'")
    print("- '检查我的即梦积分余额'")
    
    print("\n🔧 如果需要修改 token，请运行:")
    print(f"python {os.path.basename(__file__)} 您的新token")
    
    return config_file

if __name__ == "__main__":
    try:
        config_file = main()
        print(f"\n🎉 配置生成完成！配置文件: {config_file}")
    except Exception as e:
        print(f"\n❌ 配置生成失败: {e}")
        sys.exit(1)
