#!/usr/bin/env python3
"""
Basic test script to verify core functionality without MCP dependencies
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_config():
    """Test configuration module"""
    try:
        print("Testing configuration...")
        
        from jimeng_mcp_server.config import config
        print(f"✓ Config loaded. Default model: {config.default_model}")
        print(f"✓ Available models: {', '.join(config.available_models)}")
        print(f"✓ Base URL: {config.base_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def test_auth():
    """Test authentication module"""
    try:
        print("\nTesting authentication...")
        
        from jimeng_mcp_server.core.auth import TokenManager, parse_tokens_from_auth_header
        
        # Test token parsing
        tokens = parse_tokens_from_auth_header("Bearer token1,token2,token3")
        assert tokens == ["token1", "token2", "token3"], f"Expected ['token1', 'token2', 'token3'], got {tokens}"
        print("✓ Token parsing works correctly")
        
        # Test token manager
        tm = TokenManager(["test_token1", "test_token2"])
        assert tm.has_tokens(), "Token manager should have tokens"
        assert len(tm.tokens) == 2, f"Expected 2 tokens, got {len(tm.tokens)}"
        
        random_token = tm.get_random_token()
        assert random_token in ["test_token1", "test_token2"], f"Random token should be one of the configured tokens"
        print("✓ TokenManager works correctly")
        
        # Test cookie generation
        cookie = tm.generate_cookie("test_session_id")
        assert "sessionid=test_session_id" in cookie, "Cookie should contain session ID"
        print("✓ Cookie generation works correctly")
        
        # Test headers generation
        headers = tm.get_fake_headers("test_session", "/test/uri")
        assert "Cookie" in headers, "Headers should contain Cookie"
        assert "Sign" in headers, "Headers should contain Sign"
        assert "Device-Time" in headers, "Headers should contain Device-Time"
        print("✓ Headers generation works correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Auth test failed: {e}")
        return False

def test_client_structure():
    """Test client module structure (without actual HTTP calls)"""
    try:
        print("\nTesting client structure...")
        
        from jimeng_mcp_server.core.client import JimengClient, JimengAPIException, CreditInfo
        from jimeng_mcp_server.core.auth import TokenManager
        
        # Test exception class
        try:
            raise JimengAPIException("Test error", "TEST_CODE")
        except JimengAPIException as e:
            assert str(e) == "Test error", "Exception message should be correct"
            assert e.error_code == "TEST_CODE", "Exception code should be correct"
        print("✓ JimengAPIException works correctly")
        
        # Test CreditInfo model
        credit = CreditInfo(gift_credit=10, purchase_credit=20, vip_credit=30, total_credit=60)
        assert credit.total_credit == 60, "Total credit should be correct"
        print("✓ CreditInfo model works correctly")
        
        # Test client initialization
        tm = TokenManager(["test_token"])
        client = JimengClient(tm)
        assert client.token_manager == tm, "Client should use provided token manager"
        print("✓ JimengClient initialization works correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Client structure test failed: {e}")
        return False

def test_tools_structure():
    """Test tools module structure"""
    try:
        print("\nTesting tools structure...")
        
        from jimeng_mcp_server.core.client import JimengClient
        from jimeng_mcp_server.core.auth import TokenManager
        from jimeng_mcp_server.tools.images import ImageTool
        from jimeng_mcp_server.tools.chat import ChatTool
        from jimeng_mcp_server.tools.credit import CreditTool
        
        # Create mock client
        tm = TokenManager(["test_token"])
        client = JimengClient(tm)
        
        # Test tool initialization
        image_tool = ImageTool(client)
        assert image_tool.client == client, "ImageTool should use provided client"
        print("✓ ImageTool initialization works correctly")
        
        chat_tool = ChatTool(client)
        assert chat_tool.client == client, "ChatTool should use provided client"
        print("✓ ChatTool initialization works correctly")
        
        credit_tool = CreditTool(client)
        assert credit_tool.client == client, "CreditTool should use provided client"
        print("✓ CreditTool initialization works correctly")
        
        # Test model mapping
        mapping = image_tool.get_model_mapping("jimeng-3.0")
        expected = "high_aes_general_v30l:general_v3.0_18b"
        assert mapping == expected, f"Expected {expected}, got {mapping}"
        print("✓ Model mapping works correctly")
        
        # Test model parsing in chat tool
        parsed = chat_tool.parse_model("jimeng-3.0:1024x768")
        assert parsed["model"] == "jimeng-3.0", "Model name should be parsed correctly"
        assert parsed["width"] == 1024, "Width should be parsed correctly"
        assert parsed["height"] == 768, "Height should be parsed correctly"
        print("✓ Model parsing works correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Tools structure test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Running Jimeng MCP Server Basic Tests\n")
    
    tests = [
        test_config,
        test_auth,
        test_client_structure,
        test_tools_structure,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed! Core functionality is working.")
        print("\n📝 Next steps:")
        print("1. Install MCP dependencies: pip install mcp")
        print("2. Get session tokens from https://jimeng.jianying.com/")
        print("3. Run the server: python -m jimeng_mcp_server.server --tokens 'your_tokens'")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
